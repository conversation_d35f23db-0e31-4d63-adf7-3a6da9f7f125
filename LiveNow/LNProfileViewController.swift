//
//  LNProfileViewController.swift
//  LiveNow
//
//  Created by ji<PERSON><PERSON> on 2025/7/28.
//

import UIKit
import SnapKit

/// 个人页视图控制器 - 用户个人信息和设置
class LNProfileViewController: UIViewController {

    // MARK: - UI Elements
    private let scrollView = UIScrollView()
    private let contentView = UIView()

    private let headerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.clear
        return view
    }()

    private let avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "person.circle.fill")
        imageView.tintColor = UIColor.white
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = 50
        imageView.clipsToBounds = true
        imageView.backgroundColor = UIColor.white.withAlphaComponent(0.3)
        return imageView
    }()

    private let nameLabel: UILabel = {
        let label = UILabel()
        label.text = "用户名"
        label.font = UIFont.boldSystemFont(ofSize: 24)
        label.textColor = UIColor.white
        label.textAlignment = .center
        return label
    }()

    private let emailLabel: UILabel = {
        let label = UILabel()
        label.text = "<EMAIL>"
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = UIColor.white.withAlphaComponent(0.9)
        label.textAlignment = .center
        return label
    }()

    private let editProfileButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("编辑资料", for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        button.backgroundColor = UIColor.white.withAlphaComponent(0.2)
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 20
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.white.cgColor
        return button
    }()

    private let menuStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 0
        stackView.distribution = .fill
        return stackView
    }()
    // 顶部渐变层
    private let headerGradientLayer: CAGradientLayer = {
        let layer = CAGradientLayer()
        layer.colors = [
            UIColor.systemMint.withAlphaComponent(0.9).cgColor,
            UIColor.systemGreen.withAlphaComponent(0.85).cgColor
        ]
        layer.startPoint = CGPoint(x: 0, y: 0)
        layer.endPoint = CGPoint(x: 1, y: 1)
        return layer
    }()

    // 名称右侧性别年龄徽标
    private let genderAgeBadge: UILabel = {
        let label = UILabel()
        label.text = "♂︎30"
        label.font = UIFont.systemFont(ofSize: 12, weight: .semibold)
        label.textColor = .white
        label.textAlignment = .center
        label.backgroundColor = UIColor.systemBlue.withAlphaComponent(0.7)
        label.layer.cornerRadius = 10
        label.clipsToBounds = true
        label.setContentHuggingPriority(.required, for: .horizontal)
        label.setContentCompressionResistancePriority(.required, for: .horizontal)
        return label
    }()

    // 位置与ID
    private let locationLabel: UILabel = {
        let label = UILabel()
        label.text = "Indonesia"
        label.font = UIFont.systemFont(ofSize: 13)
        label.textColor = UIColor.white.withAlphaComponent(0.95)
        label.numberOfLines = 1
        return label
    }()

    private let idLabel: UILabel = {
        let label = UILabel()
        label.text = "ID:11223344"
        label.font = UIFont.systemFont(ofSize: 12)
        label.textColor = UIColor.white.withAlphaComponent(0.95)
        return label
    }()

    private let headerChevron: UIImageView = {
        let iv = UIImageView(image: UIImage(systemName: "chevron.right"))
        iv.tintColor = UIColor.white.withAlphaComponent(0.9)
        iv.contentMode = .scaleAspectFit
        return iv
    }()

    // 等级卡片
    private let levelCardView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor.systemBackground.withAlphaComponent(0.2)
        v.layer.cornerRadius = 14
        v.layer.masksToBounds = true
        v.accessibilityLabel = "levelCard"
        return v
    }()
    private let levelTitleLabel: UILabel = {
        let l = UILabel()
        l.text = "V0"
        l.font = UIFont.boldSystemFont(ofSize: 22)
        l.textColor = .white
        return l
    }()
    private let crownImageView: UIImageView = {
        let iv = UIImageView(image: UIImage(systemName: "crown"))
        iv.tintColor = .white
        iv.contentMode = .scaleAspectFit
        return iv
    }()
    private let nextLevelLabel: UILabel = {
        let l = UILabel()
        l.text = "100 to the next level"
        l.font = UIFont.systemFont(ofSize: 12)
        l.textColor = UIColor.white.withAlphaComponent(0.95)
        return l
    }()
    private let levelProgressView: UIProgressView = {
        let p = UIProgressView(progressViewStyle: .default)
        p.progressTintColor = .white
        p.trackTintColor = UIColor.white.withAlphaComponent(0.3)
        p.layer.cornerRadius = 3
        p.clipsToBounds = true
        p.progress = 0.3
        return p
    }()

    // 钻石/VIP卡片
    private let statsStackView: UIStackView = {
        let s = UIStackView()
        s.axis = .horizontal
        s.alignment = .fill
        s.distribution = .fillEqually
        s.spacing = 12
        return s
    }()

    // 菜单分组容器
    private let menuContainerView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor.secondarySystemBackground
        v.layer.cornerRadius = 16
        v.layer.masksToBounds = true
        return v
    }()
    // 统计卡片外层容器
    private let cardsContainerView: UIView = {
        let v = UIView()
        v.backgroundColor = .clear
        return v
    }()



    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupActions()
        createMenuItems()
    }

    // MARK: - Private Methods
    private func setupUI() {
        title = "我的"
        view.backgroundColor = UIColor.systemBackground

        // 设置导航栏
        navigationController?.navigationBar.prefersLargeTitles = true
        navigationItem.largeTitleDisplayMode = .always

        // 添加子视图
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)

        contentView.addSubview(headerView)
        headerView.addSubview(avatarImageView)
        headerView.addSubview(nameLabel)
        headerView.addSubview(emailLabel)
        headerView.addSubview(editProfileButton)

        contentView.addSubview(menuStackView)
    }

    private func setupConstraints() {
        // ScrollView约束
        scrollView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.leading.trailing.bottom.equalToSuperview()
        }

        // ContentView约束
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        // Header View约束
        headerView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(280)
        }

        // Avatar Image View约束
        avatarImageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(40)
            make.width.height.equalTo(100)
        }

        // Name Label约束
        nameLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarImageView.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        // Email Label约束
        emailLabel.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        // Edit Profile Button约束
        editProfileButton.snp.makeConstraints { make in
            make.top.equalTo(emailLabel.snp.bottom).offset(20)
            make.centerX.equalToSuperview()
            make.width.equalTo(120)
            make.height.equalTo(40)
        }

        // Menu Stack View约束
        menuStackView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalToSuperview().offset(-20)
        }
    }

    private func createMenuItems() {
        let menuItems = [
            ("person.crop.circle", "个人信息", "管理你的个人资料"),
            ("gear", "设置", "应用设置和偏好"),
            ("heart", "我的匹配", "查看匹配历史"),
            ("bell", "通知设置", "管理推送通知"),
            ("shield", "隐私设置", "隐私和安全设置"),
            ("questionmark.circle", "帮助与支持", "获取帮助"),
            ("info.circle", "关于应用", "版本信息和条款")
        ]

        for (index, item) in menuItems.enumerated() {
            let menuItem = createMenuItem(icon: item.0, title: item.1, subtitle: item.2, tag: index)
            menuStackView.addArrangedSubview(menuItem)

            // 添加分割线（除了最后一个）
            if index < menuItems.count - 1 {
                let separator = createSeparator()
                menuStackView.addArrangedSubview(separator)
            }
        }
    }

    private func createMenuItem(icon: String, title: String, subtitle: String, tag: Int) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = UIColor.systemBackground
        containerView.tag = tag

        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: icon)
        iconImageView.tintColor = UIColor.systemBlue
        iconImageView.contentMode = .scaleAspectFit

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = UIColor.label

        let subtitleLabel = UILabel()
        subtitleLabel.text = subtitle
        subtitleLabel.font = UIFont.systemFont(ofSize: 14)
        subtitleLabel.textColor = UIColor.secondaryLabel

        let chevronImageView = UIImageView()
        chevronImageView.image = UIImage(systemName: "chevron.right")
        chevronImageView.tintColor = UIColor.systemGray3
        chevronImageView.contentMode = .scaleAspectFit

        containerView.addSubview(iconImageView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(subtitleLabel)
        containerView.addSubview(chevronImageView)

        // 使用SnapKit设置约束
        containerView.snp.makeConstraints { make in
            make.height.equalTo(70)
        }

        iconImageView.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(20)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }

        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(iconImageView.snp.trailing).offset(16)
            make.top.equalToSuperview().offset(16)
            make.trailing.equalTo(chevronImageView.snp.leading).offset(-16)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.leading.trailing.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
        }

        chevronImageView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-20)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(12)
        }

        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(menuItemTapped(_:)))
        containerView.addGestureRecognizer(tapGesture)

        return containerView
    }

    private func createSeparator() -> UIView {
        let separator = UIView()
        separator.backgroundColor = UIColor.separator

        separator.snp.makeConstraints { make in
            make.height.equalTo(0.5)
        }

        return separator
    }

    private func setupActions() {
        editProfileButton.addTarget(self, action: #selector(editProfileTapped), for: .touchUpInside)
    }

    @objc private func editProfileTapped() {
        let alert = UIAlertController(title: "编辑资料", message: "个人资料编辑功能即将上线！", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    @objc private func menuItemTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }

        let menuTitles = ["个人信息", "设置", "我的匹配", "通知设置", "隐私设置", "帮助与支持", "关于应用"]
        let title = menuTitles[view.tag]

        let alert = UIAlertController(title: title, message: "该功能即将上线，敬请期待！", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}
//
//  LNMatchViewController.swift
//  LiveNow
//
//  Created by ji<PERSON><PERSON> on 2025/7/28.
//

import UIKit
import SnapKit

/// 匹配页视图控制器 - 智能匹配功能
class LNMatchViewController: UIViewController {
    
    // MARK: - UI Elements
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.text = "发现你的理想匹配"
        label.font = UIFont.boldSystemFont(ofSize: 24)
        label.textColor = UIColor.label
        label.textAlignment = .center
        return label
    }()
    
    private let heartImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "heart.fill")
        imageView.tintColor = UIColor.systemPink
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private let statusLabel: UILabel = {
        let label = UILabel()
        label.text = "正在为你寻找最佳匹配..."
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = UIColor.secondaryLabel
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    private let startMatchingButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("💝 开始匹配", for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        button.backgroundColor = UIColor.systemPink
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 25
        button.layer.shadowColor = UIColor.systemPink.cgColor
        button.layer.shadowOpacity = 0.3
        button.layer.shadowOffset = CGSize(width: 0, height: 2)
        button.layer.shadowRadius = 8
        return button
    }()
    
    private let filterButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("⚙️ 筛选设置", for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        button.backgroundColor = UIColor.secondarySystemBackground
        button.setTitleColor(.label, for: .normal)
        button.layer.cornerRadius = 20
        return button
    }()
    
    private let matchHistoryButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("📋 匹配历史", for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16)
        button.backgroundColor = UIColor.secondarySystemBackground
        button.setTitleColor(.label, for: .normal)
        button.layer.cornerRadius = 20
        return button
    }()
    
    private let tipsView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.systemBlue.withAlphaComponent(0.1)
        view.layer.cornerRadius = 12
        return view
    }()
    
    private let tipsLabel: UILabel = {
        let label = UILabel()
        label.text = "💡 小贴士：完善个人资料可以提高匹配成功率哦！"
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor.systemBlue
        label.numberOfLines = 0
        label.textAlignment = .center
        return label
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupActions()
    }
    
    // MARK: - Private Methods
    private func setupUI() {
        title = "匹配"
        view.backgroundColor = UIColor.systemBackground
        
        // 设置导航栏
        navigationController?.navigationBar.prefersLargeTitles = true
        navigationItem.largeTitleDisplayMode = .always
        
        // 添加子视图
        view.addSubview(titleLabel)
        view.addSubview(heartImageView)
        view.addSubview(statusLabel)
        view.addSubview(startMatchingButton)
        view.addSubview(filterButton)
        view.addSubview(matchHistoryButton)
        view.addSubview(tipsView)
        
        tipsView.addSubview(tipsLabel)
    }
    
    private func setupConstraints() {
        // Title Label约束
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide).offset(40)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        // Heart Image View约束
        heartImageView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(40)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(100)
        }
        
        // Status Label约束
        statusLabel.snp.makeConstraints { make in
            make.top.equalTo(heartImageView.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        // Start Matching Button约束
        startMatchingButton.snp.makeConstraints { make in
            make.top.equalTo(statusLabel.snp.bottom).offset(40)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(50)
        }
        
        // Filter Button约束
        filterButton.snp.makeConstraints { make in
            make.top.equalTo(startMatchingButton.snp.bottom).offset(20)
            make.leading.equalToSuperview().offset(20)
            make.trailing.equalTo(view.snp.centerX).offset(-10)
            make.height.equalTo(40)
        }
        
        // Match History Button约束
        matchHistoryButton.snp.makeConstraints { make in
            make.top.equalTo(startMatchingButton.snp.bottom).offset(20)
            make.leading.equalTo(view.snp.centerX).offset(10)
            make.trailing.equalToSuperview().offset(-20)
            make.height.equalTo(40)
        }
        
        // Tips View约束
        tipsView.snp.makeConstraints { make in
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        // Tips Label约束
        tipsLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 12, left: 16, bottom: 12, right: 16))
        }
    }
    
    private func setupActions() {
        startMatchingButton.addTarget(self, action: #selector(startMatchingTapped), for: .touchUpInside)
        filterButton.addTarget(self, action: #selector(filterTapped), for: .touchUpInside)
        matchHistoryButton.addTarget(self, action: #selector(matchHistoryTapped), for: .touchUpInside)
    }
    
    @objc private func startMatchingTapped() {
        let alert = UIAlertController(title: "开始匹配", message: "匹配功能正在开发中，敬请期待！", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    @objc private func filterTapped() {
        let alert = UIAlertController(title: "筛选设置", message: "筛选设置功能即将上线！", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    @objc private func matchHistoryTapped() {
        let alert = UIAlertController(title: "匹配历史", message: "匹配历史功能即将上线！", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
} 
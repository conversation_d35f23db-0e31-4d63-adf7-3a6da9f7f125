//
//  LNVideoChatVC+Agora.swift
//  LiveNow
//
//  Created by edy on 2025/8/8.
//

import UIKit
import AgoraRtcKit

extension LNVideoChatVC {
    
    // 初始化RTC
    func configInitAgora() {
        let log = AgoraLogConfig()
        log.level = .info
        let formatter = DateFormatter()
        formatter.dateFormat = "ddMMyyyyHHmm"
        let date = formatter.string(from: Date())
        let folder = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)
        let filePath = "\(folder[0])/AgoraLogs/\(date).log"
        log.filePath = filePath
        print("声网log的文件路径:" + filePath)
        // 设置 log 的文件大小为 2MB
        log.fileSizeInKB = 2 * 1024
        let config = AgoraRtcEngineConfig()
        config.appId = ""
        config.logConfig = log

        // init AgoraRtcEngineKit
        agoraKit = AgoraRtcEngineKit.sharedEngine(with: config, delegate: self)
        
    }

    // 设置本地视频采集
    func configLocalVideo() {
        
        agoraKit.enableVideo()
        // 设置基础美颜
        let bt = AgoraBeautyOptions()
        bt.lighteningContrastLevel = .normal
        bt.lighteningLevel = 1.0
        bt.smoothnessLevel = 1.0
        bt.rednessLevel = 0.1
        agoraKit.setBeautyEffectOptions(true, options: bt)
        
        // 设计者本地采集
        let view = UIView(frame: CGRect(origin: CGPoint(x: 0, y: 0), size: localContainer.frame.size))
        localVideo = AgoraRtcVideoCanvas()
        localVideo!.view = view
        localVideo!.renderMode = .hidden
        localVideo!.uid = 0
        localContainer.addSubview(localVideo!.view!)
        agoraKit.setupLocalVideo(localVideo)
        agoraKit.startPreview()
    }
    
    // 加入频道
    func joinAgoraChannel() {
        let options = AgoraRtcChannelMediaOptions()
        // 设置频道场景为直播
        options.channelProfile = .liveBroadcasting
        // 设置用户角色为主播；如果要将用户角色设置为观众，保持默认值即可
        options.clientRoleType = .broadcaster
        // 发布麦克风采集的音频
        options.publishMicrophoneTrack = true
        // 发布摄像头采集的视频
        options.publishCameraTrack = true
        // 自动订阅所有音频流
        options.autoSubscribeAudio = true
        // 自动订阅所有视频流
        options.autoSubscribeVideo = true
        
        let code = agoraKit.joinChannel(byToken: "", channelId: "", info: nil, uid: UInt(0)) { channel, uid, ela in
            print("加入房间成功")
        }
        
        UIApplication.shared.isIdleTimerDisabled = true

    }
    
    // 移除试图
    func removeFromParent(_ canvas: AgoraRtcVideoCanvas?) -> UIView? {
        if let it = canvas, let view = it.view {
            let parent = view.superview
            if parent != nil {
                view.removeFromSuperview()
                return parent
            }
        }
        return nil
    }
    
    // 离开频道
    func leavelChannel() {
        agoraKit.stopPreview()
        agoraKit.leaveChannel(nil)

        UIApplication.shared.isIdleTimerDisabled = false
    }
    
    func clearAgoraKit() {
        
        AgoraRtcEngineKit.destroy()
    }
    

}

extension LNVideoChatVC: AgoraRtcEngineDelegate {
    
    // 远端用户加入频道
    func rtcEngine(_ engine: AgoraRtcEngineKit, didJoinedOfUid uid: UInt, elapsed: Int) {
        
        let parent: UIView = remoteContainer
        
        if remoteVideo != nil {
            return
        }
        
        let view = UIView(frame: CGRect(origin: CGPoint(x: 0, y: 0), size: parent.frame.size))
        remoteVideo = AgoraRtcVideoCanvas()
        remoteVideo!.view = view
        remoteVideo!.renderMode = .hidden
        remoteVideo!.uid = uid
        parent.addSubview(remoteVideo!.view!)
        agoraKit.setupRemoteVideo(remoteVideo!)
    }
    // 远端用户离开频道
    func rtcEngine(_ engine: AgoraRtcEngineKit, didOfflineOfUid uid: UInt, reason: AgoraUserOfflineReason) {
        if let it = remoteVideo, it.uid == uid {
            _ = removeFromParent(it)
            remoteVideo = nil
        }
    }
    
    func rtcEngine(_ engine: AgoraRtcEngineKit, didVideoMuted muted: Bool, byUid: UInt) {
        
    }
    
    // 接收到远端用户
    func rtcEngine(_ engine: AgoraRtcEngineKit, firstRemoteVideoFrameOfUid uid: UInt, size: CGSize, elapsed: Int) {
        
    }
    
    // rtc状态
    func rtcEngine(_ engine: AgoraRtcEngineKit, didOccurWarning warningCode: AgoraWarningCode) {
        
    }
    
    // 报错
    func rtcEngine(_ engine: AgoraRtcEngineKit, didOccurError errorCode: AgoraErrorCode) {
        
    }
    
}

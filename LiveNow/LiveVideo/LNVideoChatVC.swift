//
//  LNVideoChatVC.swift
//  LiveNow
//
//  Created by edy on 2025/8/8.
//

import UIKit
import AgoraRtcKit

class LNVideoChatVC: UIViewController {

    // RTC引擎
    var agoraKit: AgoraRtcEngineKit!
    var localVideo: AgoraRtcVideoCanvas?
    var remoteVideo: AgoraRtcVideoCanvas?
    
    /// 视频聊时长统计
    var lnTimer: LNSwiftTimer?
    /// 视频聊时长
    var videoDuration: Int = 0
    
    
    override func viewDidLoad() {
        super.viewDidLoad()

        
    }
    
    
    private func layoutUI() {
        view.addSubview(remoteContainer)
        view.addSubview(localContainer)
    }
    
   

    
    // MARK: -
    lazy var localContainer: UIView = {
        let view = UIView(frame: CGRect(x: 20, y: 0, width: 80, height: 100))
        view.backgroundColor = .lightGray
        let tap = UITapGestureRecognizer(target: self, action: #selector(didClickLocalContainer))
        view.addGestureRecognizer(tap)
        return view
    }()
    lazy var remoteContainer: UIView = {
        let view = UIView(frame: CGRect(x: 0, y: 0, width: self.view.frame.size.width, height: self.view.frame.size.height))
        view.backgroundColor = .lightGray
        let tap = UITapGestureRecognizer(target: self, action: #selector(didClickRemoteContainer))
        view.addGestureRecognizer(tap)
        return view
    }()

}

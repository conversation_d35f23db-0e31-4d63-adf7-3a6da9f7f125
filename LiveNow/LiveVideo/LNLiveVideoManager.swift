//
//  LNLiveVideoManager.swift
//  LiveNow
//
//  Created by edy on 2025/8/8.
//

import UIKit
import AgoraRtcKit

enum LNLiveRole {
    case inviter    // 邀请方
    case receiver   // 接收方
}

class LNLiveVideoManager: NSObject {
    /// 单例
    static let shared = LNLiveVideoManager()
    private override init() {}

    
    @Published var active = false {
        didSet {
//            self.active ? joinTalkRoom() : exitTalkRoom()
        }
    }
}


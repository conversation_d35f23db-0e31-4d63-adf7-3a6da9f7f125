# Uncomment the next line to define a global platform for your project
platform :ios, '13.0'

post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.0'
    end
    if target.name == 'HandyJSON'
      target.build_configurations.each do |config|
        config.build_settings['SWIFT_COMPILATION_MODE'] = 'incremental'
      end
    end
  end
end

target 'LiveNow' do
  # Comment the next line if you don't want to use dynamic frameworks
  use_frameworks!

  # Pods for LiveNow
  # 网络请求框架
  pod 'Alamofire', '~> 5.9.1'
  
  # JSON解析框架
  pod 'HandyJSON', '~> 5.0.2'

  # 布局框架
  pod 'SnapKit', '~> 5.0.1'

  # 声网
  pod 'AgoraLite_iOS', '~> 4.5.2'

end
